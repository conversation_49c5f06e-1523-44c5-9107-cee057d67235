#!/usr/bin/env python3
"""
Test script to verify that the is_llm_entry attribute is correctly added to OpenAI spans.
"""

import os
import sys
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import SimpleSpanProcessor
from opentelemetry.sdk.trace.export.in_memory_span_exporter import InMemorySpanExporter
from opentelemetry import trace
from opentelemetry.instrumentation.openai import OpenAIInstrumentor

# Mock OpenAI client for testing
class MockOpenAIResponse:
    def __init__(self):
        self.choices = [MockChoice()]
        self.model = "gpt-3.5-turbo"
        self.usage = MockUsage()

class MockChoice:
    def __init__(self):
        self.message = MockMessage()

class MockMessage:
    def __init__(self):
        self.content = "This is a test response"
        self.role = "assistant"

class MockUsage:
    def __init__(self):
        self.prompt_tokens = 10
        self.completion_tokens = 20
        self.total_tokens = 30

class MockOpenAIClient:
    def __init__(self):
        self.chat = MockChat()
        self.completions = MockCompletions()
        self.embeddings = MockEmbeddings()

class MockChat:
    def __init__(self):
        self.completions = MockChatCompletions()

class MockChatCompletions:
    def create(self, **kwargs):
        return MockOpenAIResponse()

class MockCompletions:
    def create(self, **kwargs):
        return MockOpenAIResponse()

class MockEmbeddings:
    def create(self, **kwargs):
        return MockEmbeddingResponse()

class MockEmbeddingResponse:
    def __init__(self):
        self.data = [{"embedding": [0.1, 0.2, 0.3]}]
        self.model = "text-embedding-ada-002"
        self.usage = MockUsage()

def test_is_llm_entry_attribute():
    """Test that is_llm_entry attribute is added to entry spans."""
    
    # Set up tracing
    exporter = InMemorySpanExporter()
    tracer_provider = TracerProvider()
    tracer_provider.add_span_processor(SimpleSpanProcessor(exporter))
    trace.set_tracer_provider(tracer_provider)
    
    # Instrument OpenAI
    OpenAIInstrumentor().instrument()
    
    # Mock OpenAI client
    client = MockOpenAIClient()
    
    try:
        # Test 1: Direct OpenAI call (should be entry span)
        print("Test 1: Direct OpenAI chat call")
        client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": "Hello"}]
        )
        
        spans = exporter.get_finished_spans()
        assert len(spans) == 1, f"Expected 1 span, got {len(spans)}"
        
        chat_span = spans[0]
        print(f"Span name: {chat_span.name}")
        print(f"Span attributes: {dict(chat_span.attributes)}")
        
        # Check if is_llm_entry attribute is present and True
        assert "is_llm_entry" in chat_span.attributes, "is_llm_entry attribute not found"
        assert chat_span.attributes["is_llm_entry"] == True, "is_llm_entry should be True for entry span"
        
        print("✓ Test 1 passed: is_llm_entry=True for direct call")
        
        # Clear spans for next test
        exporter.clear()
        
        # Test 2: Nested OpenAI call (should not have is_llm_entry)
        print("\nTest 2: Nested OpenAI call within existing span")
        tracer = trace.get_tracer(__name__)
        
        with tracer.start_as_current_span("parent_span") as parent_span:
            # Set LLM_REQUEST_TYPE to simulate being in an LLM context
            parent_span.set_attribute("gen_ai.request.type", "chat")
            
            client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": "Hello nested"}]
            )
        
        spans = exporter.get_finished_spans()
        assert len(spans) == 2, f"Expected 2 spans, got {len(spans)}"
        
        # Find the OpenAI span (not the parent span)
        openai_span = None
        for span in spans:
            if span.name.startswith("openai."):
                openai_span = span
                break
        
        assert openai_span is not None, "OpenAI span not found"
        print(f"Nested span name: {openai_span.name}")
        print(f"Nested span attributes: {dict(openai_span.attributes)}")
        
        # Check that is_llm_entry attribute is NOT present (or False)
        if "is_llm_entry" in openai_span.attributes:
            assert openai_span.attributes["is_llm_entry"] == False, "is_llm_entry should be False for nested call"
        else:
            print("✓ is_llm_entry attribute not present for nested call (as expected)")
        
        print("✓ Test 2 passed: Nested call handled correctly")
        
        # Clear spans for next test
        exporter.clear()
        
        # Test 3: Test with completions API
        print("\nTest 3: Direct completions call")
        client.completions.create(
            model="gpt-3.5-turbo-instruct",
            prompt="Hello"
        )
        
        spans = exporter.get_finished_spans()
        assert len(spans) == 1, f"Expected 1 span, got {len(spans)}"
        
        completion_span = spans[0]
        print(f"Completion span name: {completion_span.name}")
        print(f"Completion span attributes: {dict(completion_span.attributes)}")
        
        assert "is_llm_entry" in completion_span.attributes, "is_llm_entry attribute not found in completion span"
        assert completion_span.attributes["is_llm_entry"] == True, "is_llm_entry should be True for completion entry span"
        
        print("✓ Test 3 passed: is_llm_entry=True for completions call")
        
        # Clear spans for next test
        exporter.clear()
        
        # Test 4: Test with embeddings API
        print("\nTest 4: Direct embeddings call")
        client.embeddings.create(
            model="text-embedding-ada-002",
            input="Hello world"
        )
        
        spans = exporter.get_finished_spans()
        assert len(spans) == 1, f"Expected 1 span, got {len(spans)}"
        
        embedding_span = spans[0]
        print(f"Embedding span name: {embedding_span.name}")
        print(f"Embedding span attributes: {dict(embedding_span.attributes)}")
        
        assert "is_llm_entry" in embedding_span.attributes, "is_llm_entry attribute not found in embedding span"
        assert embedding_span.attributes["is_llm_entry"] == True, "is_llm_entry should be True for embedding entry span"
        
        print("✓ Test 4 passed: is_llm_entry=True for embeddings call")
        
        print("\n🎉 All tests passed! The is_llm_entry attribute is working correctly.")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up
        OpenAIInstrumentor().uninstrument()
    
    return True

if __name__ == "__main__":
    success = test_is_llm_entry_attribute()
    sys.exit(0 if success else 1)
