[tool.commitizen]
name = "cz_conventional_commits"
tag_format = "v$version"
version_scheme = "pep440"
major_version_zero = true
update_changelog_on_bump = true
version = "0.41.0"
version_files = [
    "packages/opentelemetry-instrumentation-mcp/pyproject.toml:^version",
    "packages/opentelemetry-instrumentation-mcp/opentelemetry/instrumentation/mcp/version.py",
    "packages/opentelemetry-instrumentation-groq/pyproject.toml:^version",
    "packages/opentelemetry-instrumentation-groq/opentelemetry/instrumentation/groq/version.py",
    "packages/opentelemetry-instrumentation-alephalpha/pyproject.toml:^version",
    "packages/opentelemetry-instrumentation-alephalpha/opentelemetry/instrumentation/alephalpha/version.py",
    "packages/opentelemetry-instrumentation-anthropic/pyproject.toml:^version",
    "packages/opentelemetry-instrumentation-anthropic/opentelemetry/instrumentation/anthropic/version.py",
    "packages/opentelemetry-instrumentation-bedrock/pyproject.toml:^version",
    "packages/opentelemetry-instrumentation-bedrock/opentelemetry/instrumentation/bedrock/version.py",
    "packages/opentelemetry-instrumentation-chromadb/pyproject.toml:^version",
    "packages/opentelemetry-instrumentation-chromadb/opentelemetry/instrumentation/chromadb/version.py",
    "packages/opentelemetry-instrumentation-cohere/pyproject.toml:^version",
    "packages/opentelemetry-instrumentation-cohere/opentelemetry/instrumentation/cohere/version.py",
    "packages/opentelemetry-instrumentation-crewai/pyproject.toml:^version",
    "packages/opentelemetry-instrumentation-crewai/opentelemetry/instrumentation/crewai/version.py",
    "packages/opentelemetry-instrumentation-google-generativeai/pyproject.toml:^version",
    "packages/opentelemetry-instrumentation-google-generativeai/opentelemetry/instrumentation/google-generativeai/version.py",
    "packages/opentelemetry-instrumentation-haystack/pyproject.toml:^version",
    "packages/opentelemetry-instrumentation-haystack/opentelemetry/instrumentation/haystack/version.py",
    "packages/opentelemetry-instrumentation-langchain/pyproject.toml:^version",
    "packages/opentelemetry-instrumentation-langchain/opentelemetry/instrumentation/langchain/version.py",
    "packages/opentelemetry-instrumentation-lancedb/pyproject.toml:^version",
    "packages/opentelemetry-instrumentation-lancedb/opentelemetry/instrumentation/lancedb/version.py",
    "packages/opentelemetry-instrumentation-milvus/pyproject.toml:^version",
    "packages/opentelemetry-instrumentation-milvus/opentelemetry/instrumentation/milvus/version.py",
    "packages/opentelemetry-instrumentation-mistralai/pyproject.toml:^version",
    "packages/opentelemetry-instrumentation-mistralai/opentelemetry/instrumentation/mistralai/version.py",
    "packages/opentelemetry-instrumentation-ollama/pyproject.toml:^version",
    "packages/opentelemetry-instrumentation-ollama/opentelemetry/instrumentation/ollama/version.py",
    "packages/opentelemetry-instrumentation-openai/pyproject.toml:^version",
    "packages/opentelemetry-instrumentation-openai/opentelemetry/instrumentation/openai/version.py",
    "packages/opentelemetry-instrumentation-openai-agents/pyproject.toml:^version",
    "packages/opentelemetry-instrumentation-openai-agents/opentelemetry/instrumentation/openai_agents/version.py",
    "packages/opentelemetry-instrumentation-pinecone/pyproject.toml:^version",
    "packages/opentelemetry-instrumentation-pinecone/opentelemetry/instrumentation/pinecone/version.py",
    "packages/opentelemetry-instrumentation-marqo/pyproject.toml:^version",
    "packages/opentelemetry-instrumentation-marqo/opentelemetry/instrumentation/marqo/version.py",
    "packages/opentelemetry-instrumentation-qdrant/pyproject.toml:^version",
    "packages/opentelemetry-instrumentation-qdrant/opentelemetry/instrumentation/qdrant/version.py",
    "packages/opentelemetry-instrumentation-replicate/pyproject.toml:^version",
    "packages/opentelemetry-instrumentation-replicate/opentelemetry/instrumentation/replicate/version.py",
    "packages/opentelemetry-instrumentation-sagemaker/pyproject.toml:^version",
    "packages/opentelemetry-instrumentation-sagemaker/opentelemetry/instrumentation/sagemaker/version.py",
    "packages/opentelemetry-instrumentation-together/pyproject.toml:^version",
    "packages/opentelemetry-instrumentation-together/opentelemetry/instrumentation/together/version.py",
    "packages/opentelemetry-instrumentation-transformers/pyproject.toml:^version",
    "packages/opentelemetry-instrumentation-transformers/opentelemetry/instrumentation/transformers/version.py",
    "packages/opentelemetry-instrumentation-vertexai/pyproject.toml:^version",
    "packages/opentelemetry-instrumentation-vertexai/opentelemetry/instrumentation/vertexai/version.py",
    "packages/opentelemetry-instrumentation-watsonx/pyproject.toml:^version",
    "packages/opentelemetry-instrumentation-watsonx/opentelemetry/instrumentation/watsonx/version.py",
    "packages/opentelemetry-instrumentation-weaviate/pyproject.toml:^version",
    "packages/opentelemetry-instrumentation-weaviate/opentelemetry/instrumentation/weaviate/version.py",
    "packages/opentelemetry-instrumentation-llamaindex/pyproject.toml:^version",
    "packages/opentelemetry-instrumentation-llamaindex/opentelemetry/instrumentation/llamaindex/version.py",
    "packages/traceloop-sdk/pyproject.toml:^version",
    "packages/traceloop-sdk/traceloop/sdk/version.py",
]
