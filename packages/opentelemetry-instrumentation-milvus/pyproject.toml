[tool.coverage.run]
branch = true
source = ["opentelemetry/instrumentation/milvus"]

[tool.coverage.report]
exclude_lines = ['if TYPE_CHECKING:']
show_missing = true

[tool.poetry]
name = "opentelemetry-instrumentation-milvus"
version = "0.41.0"
description = "OpenTelemetry Milvus instrumentation"
authors = [
  "<PERSON><PERSON> <<EMAIL>>",
  "<PERSON><PERSON> <<EMAIL>>",
  "<PERSON><PERSON> <<EMAIL>>",
]
repository = "https://github.com/traceloop/openllmetry/tree/main/packages/opentelemetry-instrumentation-milvus"
license = "Apache-2.0"
readme = "README.md"

[[tool.poetry.packages]]
include = "opentelemetry/instrumentation/milvus"

[tool.poetry.dependencies]
python = ">=3.9,<4"
opentelemetry-api = "^1.28.0"
opentelemetry-semantic-conventions = ">=0.50b0"
opentelemetry-instrumentation = ">=0.50b0"
opentelemetry-semantic-conventions-ai = "0.4.10"

[tool.poetry.group.dev.dependencies]
autopep8 = "^2.2.0"
flake8 = "7.1.2"
pytest = "^8.2.2"
pytest-sugar = "1.0.0"
pymilvus = "^2.4.1"

[tool.poetry.group.test.dependencies]
pymilvus = "^2.4.1"
pytest = "^8.2.2"
pytest-sugar = "1.0.0"
opentelemetry-sdk = "^1.27.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.extras]
instruments = ["milvus"]

[tool.poetry.plugins."opentelemetry_instrumentor"]
milvus = "opentelemetry.instrumentation.milvus:MilvusInstrumentor"
