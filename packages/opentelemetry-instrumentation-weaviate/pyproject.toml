[tool.coverage.run]
branch = true
source = ["opentelemetry/instrumentation/weaviate"]

[tool.coverage.report]
exclude_lines = ['if TYPE_CHECKING:']
show_missing = true

[tool.poetry]
name = "opentelemetry-instrumentation-weaviate"
version = "0.41.0"
description = "OpenTelemetry Weaviate instrumentation"
authors = [
  "<PERSON><PERSON> <<EMAIL>>",
  "<PERSON><PERSON> <<EMAIL>>",
  "<PERSON><PERSON> <<EMAIL>>",
]
repository = "https://github.com/traceloop/openllmetry/tree/main/packages/opentelemetry-instrumentation-weaviate"
license = "Apache-2.0"
readme = "README.md"

[[tool.poetry.packages]]
include = "opentelemetry/instrumentation/weaviate"

[tool.poetry.dependencies]
python = ">=3.9,<4"
opentelemetry-api = "^1.28.0"
opentelemetry-instrumentation = ">=0.50b0"
opentelemetry-semantic-conventions = ">=0.50b0"
opentelemetry-semantic-conventions-ai = "0.4.10"

[tool.poetry.group.dev.dependencies]
autopep8 = "^2.2.0"
flake8 = "7.0.0"
pytest = "^8.2.2"
pytest-sugar = "1.0.0"

[tool.poetry.group.test.dependencies]
openai = "^1.31.1"
pytest = "^8.2.2"
pytest-sugar = "1.0.0"
vcrpy = "^6.0.1"
pytest-recording = "^0.13.1"
opentelemetry-sdk = "^1.27.0"
opentelemetry-instrumentation-openai = { path = "../opentelemetry-instrumentation-openai", develop = true }
weaviate-client = ">=3.26,<5.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.extras]
instruments = ["weaviate-client"]

[tool.poetry.plugins."opentelemetry_instrumentor"]
weaviate_client = "opentelemetry.instrumentation.weaviate:WeaviateInstrumentor"
