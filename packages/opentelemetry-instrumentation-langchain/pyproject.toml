[tool.coverage.run]
branch = true
source = ["opentelemetry/instrumentation/langchain"]

[tool.coverage.report]
exclude_lines = ['if TYPE_CHECKING:']
show_missing = true

[tool.poetry]
name = "opentelemetry-instrumentation-langchain"
version = "0.41.0"
description = "OpenTelemetry Langchain instrumentation"
authors = [
  "<PERSON><PERSON> <<EMAIL>>",
  "<PERSON><PERSON> <<EMAIL>>",
  "<PERSON><PERSON> <<EMAIL>>",
]
repository = "https://github.com/traceloop/openllmetry/tree/main/packages/opentelemetry-instrumentation-langchain"
license = "Apache-2.0"
readme = "README.md"

[[tool.poetry.packages]]
include = "opentelemetry/instrumentation/langchain"

[tool.poetry.dependencies]
python = ">=3.9,<4"
opentelemetry-api = "^1.28.0"
opentelemetry-instrumentation = ">=0.50b0"
opentelemetry-semantic-conventions = ">=0.50b0"
opentelemetry-semantic-conventions-ai = "0.4.10"

[tool.poetry.group.dev.dependencies]
autopep8 = "^2.3.1"
flake8 = "7.1.1"
pytest = "^8.3.3"
pytest-sugar = "1.0.0"


[tool.poetry.group.test.dependencies]
langchain = "^0.3.15"
langchain-community = "^0.3.3"
openai = "^1.52.2"
pytest = "^8.3.3"
pytest-sugar = "1.0.0"
vcrpy = "^6.0.2"
pytest-recording = "^0.13.2"
pytest-asyncio = "^0.24.0"
opentelemetry-sdk = "^1.27.0"
opentelemetry-instrumentation-openai = { path = "../opentelemetry-instrumentation-openai", develop = true }
opentelemetry-instrumentation-bedrock = { path = "../opentelemetry-instrumentation-bedrock", develop = true }
text-generation = "^0.7.0"
anthropic = "^0.51.0"
boto3 = "^1.35.49"
langchain-anthropic = "^0.3.13"
langchain-aws = "^0.2.11"
langchain-openai = "^0.3.1"
langchain-cohere = "0.3.1"
langchain-huggingface = "^0.1.2"
pydantic = "^2.10.5"
langchainhub = "^0.1.21"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.extras]
instruments = ["langchain"]

[tool.poetry.plugins."opentelemetry_instrumentor"]
langchain = "opentelemetry.instrumentation.langchain:LangchainInstrumentor"
